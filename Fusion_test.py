import torch
import torch.nn as nn
import torch.nn.functional as F

class SqueezeAndExcitation(nn.Module):
    def __init__(self, channel, reduction=16, activation=nn.ReLU(inplace=True)):
        super(SqueezeAndExcitation, self).__init__()
        self.fc = nn.Sequential(
            nn.Conv2d(channel, channel // reduction, kernel_size=1),
            activation,
            nn.Conv2d(channel // reduction, channel, kernel_size=1),
            nn.Sigmoid()
        )

    def forward(self, x):
        weighting = F.adaptive_avg_pool2d(x, 1)
        weighting = self.fc(weighting)
        y = x * weighting
        return y

class TIM(nn.Module):
    def __init__(self, channels_in, activation=nn.ReLU(inplace=True)):
        super(TIM, self).__init__()
        self.se_rgb = SqueezeAndExcitation(channels_in, activation=activation)
        self.se_depth = SqueezeAndExcitation(channels_in, activation=activation)
        self.alpha = nn.Parameter(torch.tensor(0.5))

    def forward(self, rgb, depth):
        rgb = self.se_rgb(rgb)
        depth = self.se_depth(depth)
        out = self.alpha * rgb + (1 - self.alpha) * depth
        return out, rgb, depth

class FusionModel(nn.Module):
    def __init__(self, channels_in=30, target_size=(15, 15)):
        super(FusionModel, self).__init__()
        self.tim = TIM(channels_in=channels_in)
        self.target_size = target_size

        # 用于分支 2 的融合（将三个不同尺寸的特征图融合）
        self.conv_fuse = nn.Conv2d(channels_in * 3, channels_in, kernel_size=1)

    def forward(self, branch1_out, branch2_out):
        # branch1_out: [64, 225, 30] -> [64, 30, 15, 15]
        batch_size, spatial_dim1, channels = branch1_out.shape
        branch1_out = branch1_out.permute(0, 2, 1).view(batch_size, channels, 15, 15)

        # branch2_out: [64, 419, 30] -> 分离为 169 (13x13), 121 (11x11), 81 (9x9)
        batch_size, spatial_dim2, channels = branch2_out.shape
        feat_13x13 = branch2_out[:, :169, :].permute(0, 2, 1).view(batch_size, channels, 13, 13)
        feat_11x11 = branch2_out[:, 169:169+121, :].permute(0, 2, 1).view(batch_size, channels, 11, 11)
        feat_9x9 = branch2_out[:, 169+121:, :].permute(0, 2, 1).view(batch_size, channels, 9, 9)

        # 上采样到目标尺寸 [15, 15]
        feat_13x13 = F.interpolate(feat_13x13, size=self.target_size, mode='bilinear', align_corners=False)
        feat_11x11 = F.interpolate(feat_11x11, size=self.target_size, mode='bilinear', align_corners=False)
        feat_9x9 = F.interpolate(feat_9x9, size=self.target_size, mode='bilinear', align_corners=False)

        # 拼接三个特征图并通过 1x1 卷积融合
        branch2_out = torch.cat([feat_13x13, feat_11x11, feat_9x9], dim=1)  # [64, 30*3, 15, 15]
        branch2_out = self.conv_fuse(branch2_out)  # [64, 30, 15, 15]

        # 应用 TIM 模块
        fused_out, rgb_out, depth_out = self.tim(branch1_out, branch2_out)

        # 可选：将输出展平为 3D 张量 [64, 225, 30]，以匹配分支 1 的格式
        fused_out = fused_out.view(batch_size, channels, -1).permute(0, 2, 1)  # [64, 225, 30]

        return fused_out

# 示例用法
if __name__ == "__main__":
    # 模拟输入
    branch1_out = torch.randn(64, 225, 30)  # 分支 1 输出
    branch2_out = torch.randn(64, 419, 30)  # 分支 2 输出

    # 初始化模型
    model = FusionModel(channels_in=30, target_size=(15, 15))

    # 前向传播
    fused_out = model(branch1_out, branch2_out)
    print(fused_out.shape)  # 输出: [64, 225, 30]