# 多数据集连续运行配置说明

## 概述
本项目现在支持一次性连续运行多个高光谱数据集的实验，并且可以为每个数据集自定义划分方式和参数。

## 文件说明

### 1. `utils/config_multi.py` - 多数据集配置文件
这是新的配置文件，支持多数据集配置和动态切换。

### 2. `main_Mamba_multi.py` - 多数据集主函数
这是新的主函数，支持连续运行多个数据集。

## 使用方法

### 1. 配置要运行的数据集
在 `utils/config_multi.py` 中修改 `datasets_to_run` 列表：

```python
datasets_to_run = [
    'WHU_Hi_LongKou',
    'PaviaU', 
    'Indian',
    'Houston2013',
    # 'QUH-Tangdaowan',
    # 'WHU-Hi-HongHu'
]
```

### 2. 自定义数据集参数
在 `dataset_configs` 字典中为每个数据集配置参数：

```python
dataset_configs = {
    'PaviaU': {
        'num_classes': 9,
        'flag_list': [0],      # 0=按数量划分, 1=按比例划分
        'train_ratio': 0.05,   # 比例划分时的训练集比例
        'train_num': 200,      # 数量划分时每类训练样本数
        'train_epoch': 100,
        'test_epoch': 5,
    },
    # ... 其他数据集配置
}
```

### 3. 运行实验
```bash
python main_Mamba_multi.py
```

## 配置参数说明

### 数据集划分方式
- `flag_list: [0]` - 按数量划分：每类固定数量的训练样本
- `flag_list: [1]` - 按比例划分：按比例分配训练集和测试集

### 关键参数
- `num_classes`: 数据集类别数
- `train_ratio`: 比例划分时的训练集比例（0-1之间）
- `train_num`: 数量划分时每类训练样本数
- `train_epoch`: 训练轮数
- `test_epoch`: 重复实验次数

## 支持的数据集

| 数据集名称 | 类别数 | 推荐划分方式 | 推荐参数 |
|-----------|--------|-------------|----------|
| PaviaU | 9 | 数量划分 | train_num=200 |
| Indian | 16 | 数量划分 | train_num=50 |
| Houston2013 | 15 | 数量划分 | train_num=190 |
| WHU_Hi_LongKou | 9 | 数量划分 | train_num=60 |
| QUH-Tangdaowan | 18 | 比例划分 | train_ratio=0.1 |
| WHU-Hi-HongHu | 22 | 数量划分 | train_num=50 |

## 输出结果

### 1. 单个数据集结果
每个数据集的结果保存在：
```
./模型对比实验/HSI-Former/{dataset_name}/...
```

### 2. 汇总结果
所有数据集的汇总结果保存在：
```
./multi_dataset_summary.txt
```

## 动态配置方法

### 1. 运行时修改数据集配置
```python
# 更新特定数据集的配置
config.update_dataset_config('PaviaU', train_num=300, train_epoch=150)
```

### 2. 添加新数据集
```python
# 在dataset_configs中添加新数据集配置
config.dataset_configs['NewDataset'] = {
    'num_classes': 10,
    'flag_list': [0],
    'train_ratio': 0.1,
    'train_num': 100,
    'train_epoch': 100,
    'test_epoch': 5,
}

# 添加到运行列表
config.datasets_to_run.append('NewDataset')
```

## 注意事项

1. **数据路径**: 确保数据文件在正确的路径下
2. **GPU内存**: 连续运行多个数据集可能需要较大的GPU内存
3. **存储空间**: 每个数据集都会生成完整的结果文件，确保有足够的存储空间
4. **时间估算**: 根据数据集大小和配置参数，合理估算总运行时间

## 示例配置

### 快速测试配置
```python
datasets_to_run = ['WHU_Hi_LongKou']

dataset_configs = {
    'WHU_Hi_LongKou': {
        'num_classes': 9,
        'flag_list': [0],
        'train_num': 30,      # 减少训练样本
        'train_epoch': 50,    # 减少训练轮数
        'test_epoch': 3,      # 减少重复次数
    }
}
```

### 完整实验配置
```python
datasets_to_run = ['PaviaU', 'Indian', 'Houston2013', 'WHU_Hi_LongKou']

# 使用默认的dataset_configs配置
```

## 故障排除

1. **内存不足**: 减少batch_size或使用更小的模型参数
2. **数据加载失败**: 检查数据文件路径和格式
3. **配置错误**: 确保dataset_configs中包含所有要运行的数据集配置

## 扩展功能

可以根据需要扩展以下功能：
- 支持不同的模型类型组合
- 添加早停机制
- 支持分布式训练
- 添加实验进度监控
