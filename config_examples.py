"""
多数据集配置示例
使用方法：将需要的配置复制到 utils/config_multi.py 中的对应位置
"""

# ==================== 示例1: 快速测试配置 ====================
# 适用于快速验证代码和调试
quick_test_config = {
    'datasets_to_run': ['WHU_Hi_LongKou'],
    'dataset_configs': {
        'WHU_Hi_LongKou': {
            'num_classes': 9,
            'flag_list': [0],
            'train_ratio': 0.01,
            'train_num': 30,      # 减少训练样本
            'train_epoch': 20,    # 减少训练轮数
            'test_epoch': 2,      # 减少重复次数
        }
    }
}

# ==================== 示例2: 单数据集完整实验 ====================
# 适用于单个数据集的完整实验
single_dataset_config = {
    'datasets_to_run': ['PaviaU'],
    'dataset_configs': {
        'PaviaU': {
            'num_classes': 9,
            'flag_list': [0],
            'train_ratio': 0.05,
            'train_num': 200,
            'train_epoch': 100,
            'test_epoch': 10,     # 增加重复次数获得更稳定结果
        }
    }
}

# ==================== 示例3: 多数据集对比实验 ====================
# 适用于多个数据集的对比实验
multi_dataset_config = {
    'datasets_to_run': ['PaviaU', 'Indian', 'Houston2013', 'WHU_Hi_LongKou'],
    'dataset_configs': {
        'PaviaU': {
            'num_classes': 9,
            'flag_list': [0],
            'train_ratio': 0.05,
            'train_num': 200,
            'train_epoch': 100,
            'test_epoch': 5,
        },
        'Indian': {
            'num_classes': 16,
            'flag_list': [0],
            'train_ratio': 0.1,
            'train_num': 50,
            'train_epoch': 100,
            'test_epoch': 5,
        },
        'Houston2013': {
            'num_classes': 15,
            'flag_list': [0],
            'train_ratio': 0.1,
            'train_num': 190,
            'train_epoch': 100,
            'test_epoch': 5,
        },
        'WHU_Hi_LongKou': {
            'num_classes': 9,
            'flag_list': [0],
            'train_ratio': 0.01,
            'train_num': 60,
            'train_epoch': 100,
            'test_epoch': 5,
        }
    }
}

# ==================== 示例4: 小样本学习实验 ====================
# 适用于小样本学习场景
few_shot_config = {
    'datasets_to_run': ['PaviaU', 'Indian', 'WHU_Hi_LongKou'],
    'dataset_configs': {
        'PaviaU': {
            'num_classes': 9,
            'flag_list': [0],
            'train_ratio': 0.01,
            'train_num': 10,      # 极少样本
            'train_epoch': 150,   # 增加训练轮数
            'test_epoch': 10,
        },
        'Indian': {
            'num_classes': 16,
            'flag_list': [0],
            'train_ratio': 0.01,
            'train_num': 5,       # 极少样本
            'train_epoch': 150,
            'test_epoch': 10,
        },
        'WHU_Hi_LongKou': {
            'num_classes': 9,
            'flag_list': [0],
            'train_ratio': 0.005,
            'train_num': 8,
            'train_epoch': 150,
            'test_epoch': 10,
        }
    }
}

# ==================== 示例5: 比例划分实验 ====================
# 使用比例划分而非固定数量
ratio_split_config = {
    'datasets_to_run': ['PaviaU', 'Indian', 'Houston2013'],
    'dataset_configs': {
        'PaviaU': {
            'num_classes': 9,
            'flag_list': [1],     # 比例划分
            'train_ratio': 0.1,   # 10%训练，90%测试
            'train_num': 200,     # 此参数在比例划分时不使用
            'train_epoch': 100,
            'test_epoch': 5,
        },
        'Indian': {
            'num_classes': 16,
            'flag_list': [1],
            'train_ratio': 0.15,  # 15%训练，85%测试
            'train_num': 50,
            'train_epoch': 100,
            'test_epoch': 5,
        },
        'Houston2013': {
            'num_classes': 15,
            'flag_list': [1],
            'train_ratio': 0.2,   # 20%训练，80%测试
            'train_num': 190,
            'train_epoch': 100,
            'test_epoch': 5,
        }
    }
}

# ==================== 示例6: 大规模数据集实验 ====================
# 适用于大规模数据集，包含所有可用数据集
full_scale_config = {
    'datasets_to_run': ['PaviaU', 'Indian', 'Houston2013', 'WHU_Hi_LongKou', 'QUH-Tangdaowan', 'WHU-Hi-HongHu'],
    'dataset_configs': {
        'PaviaU': {
            'num_classes': 9,
            'flag_list': [0],
            'train_ratio': 0.05,
            'train_num': 200,
            'train_epoch': 100,
            'test_epoch': 5,
        },
        'Indian': {
            'num_classes': 16,
            'flag_list': [0],
            'train_ratio': 0.1,
            'train_num': 50,
            'train_epoch': 100,
            'test_epoch': 5,
        },
        'Houston2013': {
            'num_classes': 15,
            'flag_list': [0],
            'train_ratio': 0.1,
            'train_num': 190,
            'train_epoch': 100,
            'test_epoch': 5,
        },
        'WHU_Hi_LongKou': {
            'num_classes': 9,
            'flag_list': [0],
            'train_ratio': 0.01,
            'train_num': 60,
            'train_epoch': 100,
            'test_epoch': 5,
        },
        'QUH-Tangdaowan': {
            'num_classes': 18,
            'flag_list': [1],
            'train_ratio': 0.1,
            'train_num': 50,
            'train_epoch': 50,
            'test_epoch': 5,
        },
        'WHU-Hi-HongHu': {
            'num_classes': 22,
            'flag_list': [0],
            'train_ratio': 0.1,
            'train_num': 50,
            'train_epoch': 100,
            'test_epoch': 5,
        }
    }
}

# ==================== 使用说明 ====================
"""
使用方法：

1. 选择合适的配置示例
2. 复制对应的配置到 utils/config_multi.py 中
3. 根据需要修改参数

例如，使用快速测试配置：
```python
# 在 utils/config_multi.py 中修改
datasets_to_run = quick_test_config['datasets_to_run']

# 更新 dataset_configs
dataset_configs.update(quick_test_config['dataset_configs'])
```

或者直接替换整个配置：
```python
# 完全替换配置
datasets_to_run = multi_dataset_config['datasets_to_run']
dataset_configs = multi_dataset_config['dataset_configs']
```
"""

# ==================== 自定义配置模板 ====================
custom_config_template = {
    'datasets_to_run': [
        # 在这里添加要运行的数据集名称
        # 'PaviaU', 'Indian', 'Houston2013', 'WHU_Hi_LongKou', 'QUH-Tangdaowan', 'WHU-Hi-HongHu'
    ],
    'dataset_configs': {
        # 数据集配置模板
        'DatasetName': {
            'num_classes': 0,        # 数据集类别数
            'flag_list': [0],        # 0=数量划分, 1=比例划分
            'train_ratio': 0.1,      # 比例划分时的训练集比例
            'train_num': 50,         # 数量划分时每类训练样本数
            'train_epoch': 100,      # 训练轮数
            'test_epoch': 5,         # 重复实验次数
        }
    }
}

# ==================== 参数调优配置 ====================
# 适用于超参数调优实验
hyperparameter_tuning_config = {
    'datasets_to_run': ['WHU_Hi_LongKou'],  # 选择一个数据集进行调优
    'dataset_configs': {
        'WHU_Hi_LongKou': {
            'num_classes': 9,
            'flag_list': [0],
            'train_ratio': 0.01,
            'train_num': 60,
            'train_epoch': 50,       # 减少训练轮数以加快调优速度
            'test_epoch': 3,         # 减少重复次数
        }
    }
}

print("配置示例文件已创建，请根据需要选择合适的配置！")
