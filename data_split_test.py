import numpy as np
import scipy.io  # Import scipy.io for handling .mat files
from utils.config import config
from utils.data_load_operate import loadData, applyPCA, createImageCubes, splitTrainTestSet
import torch
# 设置为PaviaU数据集
config.data = 'Houston2013'

# 加载数据并检查.mat文件的键值
mat_file_path = f"./data/Houston2013/Houston.mat"  # Replace with the actual path to your .mat file
mat_data = scipy.io.loadmat(mat_file_path)
print('Keys in .mat file:', mat_data.keys())
# 加载数据
X, y = loadData()
print('原始数据形状:', X.shape)
print('原始标签形状:', y.shape)

# PCA降维
X_pca = applyPCA(X, numComponents=config.pca_components)
print('PCA后数据形状:', X_pca.shape)

# 创建图像立方体
X_cubes, y_cubes = createImageCubes(X_pca, y, windowSize=config.patch_size)
print('Patch后数据形状:', X_cubes.shape)
print('Patch后标签形状:', y_cubes.shape)

# 数据集划分
X_train, X_test, y_train, y_test = splitTrainTestSet(X_cubes, y_cubes, config.test_ratio)

print('训练集数据形状:', X_train.shape)
print('训练集标签形状:', y_train.shape)
print('测试集数据形状:', X_test.shape)
print('测试集标签形状:', y_test.shape)
