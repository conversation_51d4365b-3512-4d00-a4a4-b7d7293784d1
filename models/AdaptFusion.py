import torch
import torch.nn as nn
import torch.nn.functional as F
from einops import rearrange, repeat

class AdaptiveFeatureFusion(nn.Module):
    def __init__(self, embed_dim, reduction=16, alpha_init=0.7):
        """
        自适应特征融合模块，整合分支A(Transformer+Mamba)和分支B(RCGC)
        
        参数:
            embed_dim: 特征维度
            reduction: 通道注意力的缩减比例
            alpha_init: 分支A的初始权重(0.7表示更重视分支A)
        """
        super().__init__()
        
        # 分支A和分支B的通道注意力模块
        self.attention_A = nn.Sequential(
            nn.AdaptiveAvgPool1d(1),
            nn.Conv1d(embed_dim, embed_dim // reduction, kernel_size=1, bias=False),
            nn.ReLU(),
            nn.Conv1d(embed_dim // reduction, embed_dim, kernel_size=1, bias=False),
            nn.Sigmoid()
        )
        
        self.attention_B = nn.Sequential(
            nn.AdaptiveAvgPool1d(1),
            nn.Conv1d(embed_dim, embed_dim // reduction, kernel_size=1, bias=False),
            nn.ReLU(),
            nn.Conv1d(embed_dim // reduction, embed_dim, kernel_size=1, bias=False),
            nn.Sigmoid()
        )
        
        # 自适应权重学习
        self.alpha = nn.Parameter(torch.tensor(alpha_init))
        self.beta = nn.Parameter(torch.tensor(1.0 - alpha_init))
        
        # 特征交互
        self.interaction = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(embed_dim, embed_dim)
        )
        
        # 最终融合层
        self.final_fusion = nn.Sequential(
            nn.LayerNorm(embed_dim),
            nn.Linear(embed_dim, embed_dim),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(embed_dim, embed_dim)
        )
        
    def forward(self, branch_A, branch_B):
        """
        前向传播
        
        参数:
            branch_A: 分支A的特征 [batch_size, n, embed_dim]
            branch_B: 分支B的特征 [batch_size, n, embed_dim]
            
        返回:
            fused_feature: 融合后的特征 [batch_size, n, embed_dim]
        """
        # 应用通道注意力
        batch_size, n, embed_dim = branch_A.shape
        
        # 调整为通道优先格式以应用通道注意力
        A_for_attn = branch_A.permute(0, 2, 1)  # [batch_size, embed_dim, n]
        B_for_attn = branch_B.permute(0, 2, 1)
        
        # 计算注意力权重
        attn_A = self.attention_A(A_for_attn)  # [batch_size, embed_dim, 1]
        attn_B = self.attention_B(B_for_attn)
        
        # 应用注意力权重
        enhanced_A = branch_A * attn_A.permute(0, 2, 1)  # [batch_size, n, embed_dim]
        enhanced_B = branch_B * attn_B.permute(0, 2, 1)
        
        # 自适应加权融合
        weighted_A = self.alpha * enhanced_A
        weighted_B = self.beta * enhanced_B
        
        # 特征交互
        combined = torch.cat([weighted_A, weighted_B], dim=-1)  # [batch_size, n, embed_dim*2]
        interaction = self.interaction(combined)
        
        # 最终融合
        fused_feature = weighted_A + weighted_B + interaction
        fused_feature = self.final_fusion(fused_feature)
        
        return fused_feature