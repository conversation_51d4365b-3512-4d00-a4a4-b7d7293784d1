import torch
import torch.nn as nn
class ChannelAttentionFusion(nn.Module):
    def __init__(self, embed_dim, reduction=8):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool1d(1)
        self.fc = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // reduction, bias=False),
            nn.ReLU(),
            nn.Linear(embed_dim // reduction, embed_dim, bias=False),
            nn.Sigmoid()
        )
        
    def forward(self, x1, x2):
        # x1, x2: [B, n, embed_dim]
        # 特征相加
        x = x1 + x2  # [B, n, embed_dim]
        
        # 通道注意力计算
        b, n, c = x.size()
        y = self.avg_pool(x.transpose(1, 2)).view(b, c)  # [B, c]
        y = self.fc(y).view(b, 1, c)  # [B, 1, c]
        
        # 应用注意力权重
        fused = x1 * y + x2 * (1 - y)  # 互补加权
        
        return fused