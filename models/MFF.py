import torch
import torch.nn as nn
import torch.nn.functional as F
from einops import rearrange, repeat

class MultiScaleFeatureInteractionFusion(nn.Module):
    def __init__(self, embed_dim, reduction=8, scales=[1, 3, 5]):
        """
        多尺度交互特征融合模块，整合分支A(Transformer+Mamba)和分支B(RCGC)
        
        参数:
            embed_dim: 特征维度
            reduction: 通道注意力的缩减比例
            scales: 多尺度卷积的核大小列表
        """
        super().__init__()
        
        # 多尺度特征提取器
        self.scale_processors_A = nn.ModuleList([
            nn.Sequential(
                nn.Conv1d(embed_dim, embed_dim, kernel_size=k, padding=k//2, groups=embed_dim),
                nn.BatchNorm1d(embed_dim),
                nn.GELU()
            ) for k in scales
        ])
        
        self.scale_processors_B = nn.ModuleList([
            nn.Sequential(
                nn.Conv1d(embed_dim, embed_dim, kernel_size=k, padding=k//2, groups=embed_dim),
                nn.BatchNorm1d(embed_dim),
                nn.GELU()
            ) for k in scales
        ])
        
        # 跨分支特征交互
        self.cross_attention_A = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // reduction),
            nn.Tanh(),
            nn.Linear(embed_dim // reduction, embed_dim),
            nn.Sigmoid()
        )
        
        self.cross_attention_B = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // reduction),
            nn.Tanh(),
            nn.Linear(embed_dim // reduction, embed_dim),
            nn.Sigmoid()
        )
        
        # 特征融合门控
        self.gate_A = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),
            nn.Sigmoid()
        )
        
        self.gate_B = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),
            nn.Sigmoid()
        )
        
        # 层次化融合
        self.fusion_level1 = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.GELU()
        )
        
        self.fusion_level2 = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.GELU()
        )
        
        self.fusion_level3 = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.GELU()
        )
        
        # 最终特征增强
        self.feature_enhancement = nn.Sequential(
            nn.Linear(embed_dim * 3, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.GELU(),
            nn.Linear(embed_dim, embed_dim),
            nn.Dropout(0.1)
        )
        
    def forward(self, branch_A, branch_B):
        """
        前向传播
        
        参数:
            branch_A: 分支A的特征 [batch_size, n, embed_dim]
            branch_B: 分支B的特征 [batch_size, n, embed_dim]
            
        返回:
            fused_feature: 融合后的特征 [batch_size, n, embed_dim]
        """
        batch_size, n, embed_dim = branch_A.shape
        
        # 多尺度特征提取 (通道优先格式)
        A_processed = []
        B_processed = []
        
        A_for_conv = branch_A.permute(0, 2, 1)  # [batch_size, embed_dim, n]
        B_for_conv = branch_B.permute(0, 2, 1)
        
        for processor in self.scale_processors_A:
            A_processed.append(processor(A_for_conv).permute(0, 2, 1))  # 转回 [batch_size, n, embed_dim]
            
        for processor in self.scale_processors_B:
            B_processed.append(processor(B_for_conv).permute(0, 2, 1))
        
        # 跨分支注意力引导
        A_global = torch.mean(branch_A, dim=1, keepdim=True)  # [batch_size, 1, embed_dim]
        B_global = torch.mean(branch_B, dim=1, keepdim=True)
        
        attn_A = self.cross_attention_A(B_global)  # [batch_size, 1, embed_dim]
        attn_B = self.cross_attention_B(A_global)
        
        # 应用注意力
        enhanced_A = branch_A * attn_A
        enhanced_B = branch_B * attn_B
        
        # 计算门控
        concat_A = torch.cat([enhanced_A, enhanced_B], dim=-1)  # [batch_size, n, embed_dim*2]
        concat_B = torch.cat([enhanced_B, enhanced_A], dim=-1)
        
        gate_A = self.gate_A(concat_A)  # [batch_size, n, embed_dim]
        gate_B = self.gate_B(concat_B)
        
        # 层次化融合
        level1_features = []
        for a, b in zip(A_processed, B_processed):
            fused = self.fusion_level1(torch.cat([a * gate_A, b * gate_B], dim=-1))
            level1_features.append(fused)
        
        # 第二级融合
        level2_features = []
        for i in range(len(level1_features) - 1):
            fused = self.fusion_level2(torch.cat([level1_features[i], level1_features[i+1]], dim=-1))
            level2_features.append(fused)
        
        # 第三级融合
        if len(level2_features) > 1:
            level3_features = self.fusion_level3(torch.cat([level2_features[0], level2_features[-1]], dim=-1))
        else:
            level3_features = level2_features[0]
        
        # 最终特征增强
        if len(level1_features) >= 3:
            combined = torch.cat([level1_features[0], level2_features[0], level3_features], dim=-1)
        else:
            combined = torch.cat([level1_features[0], level3_features], dim=-1)
            
        fused_feature = self.feature_enhancement(combined)
        
        # 添加残差连接
        fused_feature = fused_feature + enhanced_A + enhanced_B
        
        return fused_feature