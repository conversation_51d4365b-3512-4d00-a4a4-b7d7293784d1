import torch
import torch.nn as nn
import torch.nn.functional as F

class SqueezeAndExcitation(nn.Module):
    def __init__(self, channel, reduction=16, activation=nn.ReLU):
        super(SqueezeAndExcitation, self).__init__()
        self.fc = nn.Sequential(
            nn.Conv2d(channel, channel // reduction, kernel_size=1),
            activation(inplace=True),
            nn.Conv2d(channel // reduction, channel, kernel_size=1),
            nn.Sigmoid()
        )

    def forward(self, x):
        weighting = F.adaptive_avg_pool2d(x, 1)
        weighting = self.fc(weighting)
        y = x * weighting
        return y

class TIM(nn.Module):
    def __init__(self, channels_in, activation=nn.ReLU):
        super(TIM, self).__init__()
        self.se_rgb = SqueezeAndExcitation(channels_in, activation=activation)
        self.se_depth = SqueezeAndExcitation(channels_in, activation=activation)
        self.alpha = nn.Parameter(torch.tensor(0.5))

    def forward(self, rgb, depth):
        rgb = self.se_rgb(rgb)
        depth = self.se_depth(depth)
        out = self.alpha * rgb + (1 - self.alpha) * depth
        return out, rgb, depth