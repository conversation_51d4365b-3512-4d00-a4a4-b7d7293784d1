import os
import torch
import torch.nn as nn
from functools import partial
from torch import Tensor
from typing import Optional
import torch.utils.checkpoint as checkpoint
from einops import rearrange, repeat
import torch.nn.functional as F
from timm.models.layers import DropPath, to_2tuple
from timm.models.vision_transformer import _cfg, register_model, _load_weights
from timm.models.layers import trunc_normal_
import math
import torch_dct as dct
from mamba_ssm.modules.mamba_simple import Mamba
from models.csms6s import SelectiveScanMamba, SelectiveScanCore, SelectiveScanOflex
from models.RCGC import RCGC
from models.fusion import TIM, SqueezeAndExcitation
from utils.config_multi import config
try:
    from mamba_ssm.ops.triton.layernorm import RMSNorm, layer_norm_fn, rms_norm_fn
except ImportError:
    RMSNorm, layer_norm_fn, rms_norm_fn = None, None, None

MODEL_PATH = 'your_model_path'
_MODELS = {
    "videomamba_t16_in1k": os.path.join(MODEL_PATH, "videomamba_t16_in1k_res224.pth"),
    "videomamba_s16_in1k": os.path.join(MODEL_PATH, "videomamba_s16_in1k_res224.pth"),
    "videomamba_m16_in1k": os.path.join(MODEL_PATH, "videomamba_m16_in1k_res224.pth"),
}

# 新增：光谱注意力模块
class SpectralAttention(nn.Module):
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool3d(1)  # 全局平均池化，压缩时间和空间维度
        self.fc = nn.Sequential(
            nn.Linear(channels, channels // reduction, bias=False),
            nn.ReLU(),
            nn.Linear(channels // reduction, channels, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, t, h, w = x.shape
        y = self.avg_pool(x).view(b, c)  # [batch_size, channels]
        y = self.fc(y).view(b, c, 1, 1, 1)  # [batch_size, channels, 1, 1, 1]
        return x * y  # 光谱通道加权

class mamba_init:
    @staticmethod
    def dt_init(dt_rank, d_inner, dt_scale=1.0, dt_init="random", dt_min=0.001, dt_max=0.1, dt_init_floor=1e-4, **factory_kwargs):
        dt_proj = nn.Linear(dt_rank, d_inner, bias=True, **factory_kwargs)
        dt_init_std = dt_rank ** -0.5 * dt_scale
        if dt_init == "constant":
            nn.init.constant_(dt_proj.weight, dt_init_std)
        elif dt_init == "random":
            nn.init.uniform_(dt_proj.weight, -dt_init_std, dt_init_std)
        else:
            raise NotImplementedError
        dt = torch.exp(
            torch.rand(d_inner, **factory_kwargs) * (math.log(dt_max) - math.log(dt_min)) + math.log(dt_min)
        ).clamp(min=dt_init_floor)
        inv_dt = dt + torch.log(-torch.expm1(-dt))
        with torch.no_grad():
            dt_proj.bias.copy_(inv_dt)
        return dt_proj

    @staticmethod
    def A_log_init(d_state, d_inner, copies=-1, device=None, merge=True):
        A = repeat(
            torch.arange(1, d_state + 1, dtype=torch.float32, device=device),
            "n -> d n", d=d_inner,
        ).contiguous()
        A_log = torch.log(A)
        if copies > 0:
            A_log = repeat(A_log, "d n -> r d n", r=copies)
            if merge:
                A_log = A_log.flatten(0, 1)
        A_log = nn.Parameter(A_log)
        A_log._no_weight_decay = True
        return A_log

    @staticmethod
    def D_init(d_inner, copies=-1, device=None, merge=True):
        D = torch.ones(d_inner, device=device)
        if copies > 0:
            D = repeat(D, "n1 -> r n1", r=copies)
            if merge:
                D = D.flatten(0, 1)
        D = nn.Parameter(D)
        D._no_weight_decay = True
        return D

class Residual(nn.Module):
    def __init__(self, fn):
        super().__init__()
        self.fn = fn

    def forward(self, x, **kwargs):
        return self.fn(x, **kwargs) + x

class LayerNormalize(nn.Module):
    def __init__(self, dim, fn):
        super().__init__()
        self.norm = nn.LayerNorm(dim)
        self.fn = fn

    def forward(self, x, **kwargs):
        return self.fn(self.norm(x), **kwargs)

class Attention(nn.Module):
    def __init__(self, dim, heads=8, dropout=0.1):
        super().__init__()
        self.heads = heads
        self.scale = dim ** -0.5
        self.to_qkv = nn.Linear(dim, dim * 3, bias=True)
        self.to_kv = nn.Linear(dim, dim * 2, bias=False)
        self.nn1 = nn.Linear(dim, dim)
        self.do1 = nn.Dropout(dropout)
        self.sr = nn.Conv2d(dim, dim, kernel_size=2, stride=2)
        self.norm = nn.LayerNorm(dim)
        self.act = nn.GELU()

    def forward(self, x, mask=None, use_SR=False):
        b, n, d, h = *x.shape, self.heads
        s = int((n-1) ** 0.5)
        c = x[:,0,:].reshape(b,1,d)
        f = x[:,1:,:]
        if use_SR:
            q = x.reshape(b, n, h, d // h).permute(0, 2, 1, 3)
            f_ = f.permute(0, 2, 1).reshape(b, d, s, s)
            f_ = self.sr(f_)
            f_ = rearrange(f_, 'b h n d -> b h (n d)').permute(0, 2, 1)
            f_ = torch.cat((c, f_), dim=1)
            f_ = self.norm(f_)
            f_ = self.act(f_)
            kv = self.to_kv(f_).chunk(2, dim=-1)
            k, v = map(lambda t: rearrange(t, 'b n (h d) -> b h n d', h=h), kv)
        else:
            qkv = self.to_qkv(x).chunk(3, dim=-1)
            q, k, v = map(lambda t: rearrange(t, 'b n (h d) -> b h n d', h=h), qkv)
        dots = torch.einsum('bhid,bhjd->bhij', q, k) * self.scale
        mask_value = -torch.finfo(dots.dtype).max
        if mask is not None:
            mask = F.pad(mask.flatten(1), (1, 0), value=True)
            assert mask.shape[-1] == dots.shape[-1], 'mask has incorrect dimensions'
            mask = mask[:, None, :] * mask[:, :, None]
            dots.masked_fill_(~mask, float('-inf'))
            del mask
        attn = dots.softmax(dim=-1)
        out = torch.einsum('bhij,bhjd->bhid', attn, v)
        out = rearrange(out, 'b h n d -> b n (h d)')
        out = self.nn1(out)
        out = self.do1(out)
        return out

class Transformer(nn.Module):
    def __init__(self, dim, depth, heads, mlp_dim, dropout):
        super().__init__()
        self.layers = nn.ModuleList([])
        for _ in range(depth):
            self.layers.append(nn.ModuleList([
                Residual(LayerNormalize(dim, Attention(dim, heads=heads, dropout=dropout))),
                Residual(LayerNormalize(dim, MLP_Block(dim, mlp_dim, dropout=dropout)))
            ]))

    def forward(self, x, mask=None):
        for attention, mlp in self.layers:
            x = attention(x, mask=mask)
            x = mlp(x)
        return x

class Block(nn.Module, mamba_init):
    def __init__(self, scan_type=None, group_type=None, k_group=None, dim=None, dt_rank="auto",
                 d_state=None, d_model=None, ssm_ratio=None, bimamba=None, seq=False, force_fp32=True,
                 dropout=0.0, **kwargs):
        super().__init__()
        act_layer = nn.SiLU
        dt_min = 0.001
        dt_max = 0.1
        dt_init = "random"
        dt_scale = 1.0
        dt_init_floor = 1e-4
        bias = False
        self.force_fp32 = force_fp32
        self.seq = seq
        self.k_group = k_group
        self.group_type = group_type
        self.scan_type = scan_type
        d_inner = int(ssm_ratio * d_model)
        dt_rank = math.ceil(d_model / 16) if dt_rank == "auto" else dt_rank
        self.in_proj = nn.Linear(dim, d_inner * 2, bias=bias, **kwargs)
        self.act = act_layer()
        self.forward_conv1d = nn.Conv1d(in_channels=d_inner, out_channels=d_inner, kernel_size=1)
        self.conv2d = nn.Conv2d(in_channels=d_inner, out_channels=d_inner, groups=d_inner, bias=True, kernel_size=(1, 1), **kwargs)
        self.conv3d = nn.Conv3d(in_channels=d_inner, out_channels=d_inner, groups=d_inner, bias=True, kernel_size=(1, 1, 1), **kwargs)
        self.x_proj = [nn.Linear(d_inner, (dt_rank + d_state * 2), bias=False, **kwargs) for _ in range(k_group)]
        self.x_proj_weight = nn.Parameter(torch.stack([t.weight for t in self.x_proj], dim=0))
        del self.x_proj
        self.dt_projs = [self.dt_init(dt_rank, d_inner, dt_scale, dt_init, dt_min, dt_max, dt_init_floor, **kwargs) for _ in range(k_group)]
        self.dt_projs_weight = nn.Parameter(torch.stack([t.weight for t in self.dt_projs], dim=0))
        self.dt_projs_bias = nn.Parameter(torch.stack([t.bias for t in self.dt_projs], dim=0))
        del self.dt_projs
        self.A_logs = self.A_log_init(d_state, d_inner, copies=k_group, merge=True)
        self.Ds = self.D_init(d_inner, copies=k_group, merge=True)
        self.out_norm = nn.LayerNorm(d_inner)
        self.out_proj = nn.Linear(d_inner, dim, bias=bias, **kwargs)
        self.dropout = nn.Dropout(dropout) if dropout > 0. else nn.Identity()

    def scan(self, x, scan_type=None, group_type=None, route=None):
        if group_type == 'Patch':
            x_hwwh = torch.stack([x.view(self.B, -1, self.L), torch.transpose(x, dim0=2, dim1=3).contiguous().view(self.B, -1, self.L)], dim=1).view(self.B, 2, -1, self.L)
            xs = torch.cat([x_hwwh, torch.flip(x_hwwh, dims=[-1])], dim=1)
        elif group_type == 'Linear':
            xs = torch.stack([x, torch.flip(x, dims=[-1])], dim=1)
        return xs

    def forward(self, x: Tensor, route=None, SelectiveScan=SelectiveScanMamba):
        x = self.in_proj(x)
        x, z = x.chunk(2, dim=-1)
        z = self.act(z)
        if self.group_type == 'Linear':
            x1_rearranged = rearrange(x, "b s d -> b d s").contiguous()
            x = self.forward_conv1d(x1_rearranged)
            x = self.act(x)
        def selective_scan(u, delta, A, B, C, D=None, delta_bias=None, delta_softplus=True, nrows=1):
            return SelectiveScan.apply(u, delta, A, B, C, D, delta_bias, delta_softplus, nrows, False)
        if len(x.size()) == 3:
            B, D, L = x.shape
        self.B = B
        self.L = L
        D, N = self.A_logs.shape
        K, D, R = self.dt_projs_weight.shape
        xs = self.scan(x, scan_type=self.scan_type, group_type=self.group_type, route=route)
        x_dbl = torch.einsum("b k d l, k c d -> b k c l", xs, self.x_proj_weight)
        dts, Bs, Cs = torch.split(x_dbl, [R, N, N], dim=2)
        dts = torch.einsum("b k r l, k d r -> b k d l", dts, self.dt_projs_weight)
        xs = xs.view(B, -1, L)
        dts = dts.contiguous().view(B, -1, L)
        Bs = Bs.contiguous()
        Cs = Cs.contiguous()
        As = -torch.exp(self.A_logs.float())
        Ds = self.Ds.float()
        dt_projs_bias = self.dt_projs_bias.float().view(-1)
        to_fp32 = lambda *args: (_a.to(torch.float32) for _a in args)
        if self.force_fp32:
            xs, dts, Bs, Cs = to_fp32(xs, dts, Bs, Cs)
        if self.seq:
            out_y = []
            for i in range(self.k_group):
                yi = selective_scan(
                    xs.view(B, K, -1, L)[:, i], dts.view(B, K, -1, L)[:, i],
                    As.view(K, -1, N)[i], Bs[:, i].unsqueeze(1), Cs[:, i].unsqueeze(1), Ds.view(K, -1)[i],
                    delta_bias=dt_projs_bias.view(K, -1)[i], delta_softplus=True,
                ).view(B, -1, L)
                out_y.append(yi)
            out_y = torch.stack(out_y, dim=1)
        else:
            out_y = selective_scan(
                xs, dts, As, Bs, Cs, Ds, delta_bias=dt_projs_bias, delta_softplus=True,
            ).view(B, K, -1, L)
        assert out_y.dtype == torch.float
        if out_y.size(1) == 2:
            y = out_y[:, 0] + torch.flip(out_y[:, 1], dims=[-1])
            y = y.transpose(dim0=1, dim1=2).contiguous()
            y = self.out_norm(y)
        y = y * z
        out = self.dropout(self.out_proj(y))
        return out

class MLP_Block(nn.Module):
    def __init__(self, in_features, hidden_features, dropout=0.1):
        super().__init__()
        self.mlp = nn.Sequential(
            nn.Linear(in_features, hidden_features),
            nn.GELU(),
            nn.Dropout(dropout) if dropout > 0. else nn.Identity(),
            nn.Linear(hidden_features, in_features)
        )

    def forward(self, x):
        return self.mlp(x)

class Mlp(nn.Module):
    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.GELU, drop=0.):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = act_layer()
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.drop = nn.Dropout(drop)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = self.drop(x)
        return x


class VisionMamba(nn.Module):
    def __init__(
            self,
            model_type=None,
            k_group=None,
            depth=None,
            embed_dim=None,
            d_state=None,
            ssm_ratio=None,
            num_classes=None,
            drop_rate=0.,
            drop_path_rate=0.1,
            fused_add_norm=False,
            residual_in_fp32=True,
            bimamba=True,
            fc_drop_rate=0.,
            use_checkpoint=False,
            checkpoint_num=0,
            Pos_Cls=False,
            pos=None,
            cls=None,
            conv3D_channel=None,
            conv3D_kernel_1=None,
            conv3D_kernel_2=None,
            conv3D_kernel_3=None,
            dim_patch=None,
            dim_linear_1=None,
            dim_linear_2=None,
            dim_linear_3=None,
            **kwargs,
    ):
        super().__init__()
        self.residual_in_fp32 = residual_in_fp32
        self.fused_add_norm = fused_add_norm
        self.use_checkpoint = use_checkpoint
        self.checkpoint_num = checkpoint_num
        self.Pos_Cls = Pos_Cls
        self.num_classes = num_classes
        self.num_features = self.embed_dim = embed_dim
        self.k_group = k_group
        self.depth = depth
        self.model_type = model_type

        # 添加光谱注意力模块
        self.spectral_attention_1 = SpectralAttention(conv3D_channel)
        self.spectral_attention_2 = SpectralAttention(conv3D_channel)
        self.spectral_attention_3 = SpectralAttention(conv3D_channel)

        # 3D 卷积层
        self.conv3d_features_1 = nn.Sequential(
            nn.Conv3d(1, out_channels=conv3D_channel, kernel_size=conv3D_kernel_1),
            nn.BatchNorm3d(conv3D_channel),
            nn.ReLU(),
        )
        self.conv3d_features_2 = nn.Sequential(
            nn.Conv3d(1, out_channels=conv3D_channel, kernel_size=conv3D_kernel_2),
            nn.BatchNorm3d(conv3D_channel),
            nn.ReLU(),
        )
        self.conv3d_features_3 = nn.Sequential(
            nn.Conv3d(1, out_channels=conv3D_channel, kernel_size=conv3D_kernel_3),
            nn.BatchNorm3d(conv3D_channel),
            nn.ReLU(),
        )

        # RCGC
        self.conv1 = nn.Conv2d(30, embed_dim, kernel_size=1, bias=False)
        self.ln = nn.LayerNorm(embed_dim)
        self.mlp = Mlp(in_features=embed_dim, hidden_features=2*embed_dim, out_features=embed_dim).to('cuda')
        self.graconv = RCGC(input_dim=embed_dim, hid_dim=embed_dim, output_dim=embed_dim, n_seq=1, p_dropout=0)
        
        # 嵌入层
        self.embedding_spatial_1 = nn.Sequential(nn.Linear(conv3D_channel * dim_linear_1, embed_dim))
        self.embedding_spatial_2 = nn.Sequential(nn.Linear(conv3D_channel * dim_linear_2, embed_dim))
        self.embedding_spatial_3 = nn.Sequential(nn.Linear(conv3D_channel * dim_linear_3, embed_dim))
        
        # TIM 融合模块
        self.tim = TIM(channels_in=embed_dim)

        # 融合分支 2 的 1x1 卷积
        self.conv_fuse = nn.Conv2d(embed_dim * 3, embed_dim, kernel_size=1)       
        
        self.norm = nn.LayerNorm(embed_dim)
        self.avgpool = nn.AdaptiveAvgPool2d(1)
        self.flatten = nn.Flatten(1)
        self.cls_token = nn.Parameter(torch.zeros(1, 1, self.embed_dim))
        self.pos_embed = nn.Parameter(torch.zeros(1, 1792 + 1, self.embed_dim))
        self.temporal_pos_embedding = nn.Parameter(torch.zeros(1, 28, embed_dim))
        self.pos_drop = nn.Dropout(p=drop_rate)
        self.head_drop = nn.Dropout(fc_drop_rate) if fc_drop_rate > 0 else nn.Identity()
        self.head = nn.Linear(self.num_features, num_classes) if num_classes > 0 else nn.Identity()
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, depth)]
        inter_dpr = [0.0] + dpr
        self.drop_path = DropPath(drop_path_rate) if drop_path_rate > 0. else nn.Identity()
        self.proj = nn.Linear(embed_dim, embed_dim, bias=False)

        # Transformer 模块
        self.transformer_1 = nn.ModuleList([Residual(LayerNormalize(embed_dim, Attention(embed_dim, heads=8, dropout=drop_path_rate)))
                                            for i in range(depth)])
        self.transformer_2 = nn.ModuleList([Residual(LayerNormalize(embed_dim, Attention(embed_dim, heads=8, dropout=drop_path_rate)))
                                            for i in range(depth)])
        self.transformer_3 = nn.ModuleList([Residual(LayerNormalize(embed_dim, Attention(embed_dim, heads=8, dropout=drop_path_rate)))
                                            for i in range(depth)])

        self.FFN = nn.ModuleList([Residual(LayerNormalize(embed_dim, MLP_Block(embed_dim, embed_dim, dropout=drop_path_rate)))
                                  for i in range(depth)])

        # Mamba 模块
        self.layers = nn.ModuleList([Block(
            group_type='Linear',
            k_group=2,
            dim=embed_dim,
            d_state=d_state,
            d_model=embed_dim,
            ssm_ratio=ssm_ratio,
            bimamba=bimamba,
            **kwargs,
        ) for i in range(depth)])

        # 初始化权重
        trunc_normal_(self.pos_embed, std=.02)
        trunc_normal_(self.cls_token, std=.02)
        trunc_normal_(self.temporal_pos_embedding, std=.02)

    def get_num_layers(self):
        return len(self.layers)

    def scan(self, x, scan_type=None, group_type=None):
        x = rearrange(x, 'b c t h w -> b (c t) h w')
        x = rearrange(x, 'b c h w -> b h w c')
        return x

    def mask_generate(self, img_size, num):
        out = []
        mask_none = torch.ones(img_size, img_size)
        for i in reversed(range(0, num - 1)):
            mask = torch.ones(img_size // (2 ** i), img_size // (2 ** i))
            mask = torch.triu(mask, diagonal=0)
            mask = torch.rot90(mask, k=1, dims=(0, 1))
            out.append(
                torch.nn.functional.pad(mask, (0, img_size - img_size // (2 ** i), 0, img_size - img_size // (2 ** i)),
                                        mode='constant', value=0))
        out.append(mask_none)
        return out

    def forward_features(self, x, inference_params=None):

        # rcgc模块
        x_01 = rearrange(x, 'b c t h w -> b (c t) h w')  #  [10, 1, 30, 15, 15]-->[10, 30, 15, 15]         
        x_02 = self.conv1(x_01)     # [10, 30, 15, 15]-->[10, embed_dim, 15, 15] 
        x__ = rearrange(x_02, 'b c h w -> b c (h w)')   # [10, embed_dim, 15, 15]-->[10, embed_dim, 225]
        x_reshape =  x__.permute(0, 2, 1)  # [10, embed_dim, 225]-->[10, 225, embed_dim]
        x_ln = self.ln(x_reshape)  # [10, 225, embed_dim]-->[10, 225, embed_dim]
        x_ln = x_ln.permute(0, 2, 1)  # [10, embed_dim, 225]
        x_g = self.graconv(x_ln) + x__    # [10, embed_dim, 225]
        #mlp处理
        x_g_reshaped = x_g.permute(0, 2, 1) # [10, embed_dim, 225]-->[10, 225, embed_dim]    
        x_c = self.mlp(x_g_reshaped)    # [10, 225, embed_dim] 
        x_c = x_c.permute(0, 2, 1) + x_g  # 恢复维度顺序并添加残差连接
        x_rcgc = x_c.permute(0, 2, 1)   # [10, 225, embed_dim] 
 
        # 记录空间尺寸
        spatial_dims = []       
        
        # 3D 卷积 + 光谱注意力
        x_1 = self.conv3d_features_1(x)             # [10, 1, 30, 15, 15]->[10, 32, 28, 11, 11]
        x_1 = self.spectral_attention_1(x_1)        
        x_2 = self.conv3d_features_2(x)
        x_2 = self.spectral_attention_2(x_2) 
        x_3 = self.conv3d_features_3(x)
        x_3 = self.spectral_attention_3(x_3)  

        # 扫描
        x_1 = self.scan(x_1)                        # [64, 11, 11, 832]
        spatial_dims.append(x_1.shape[1:3])  # 记录 (h_1, w_1)，如 (11, 11)
        x_2 = self.scan(x_2)
        spatial_dims.append(x_2.shape[1:3])  
        x_3 = self.scan(x_3)
        spatial_dims.append(x_3.shape[1:3])  

        # 嵌入
        x_1 = self.embedding_spatial_1(x_1)         # [64, 11, 11, 32]   
        x_2 = self.embedding_spatial_2(x_2)
        x_3 = self.embedding_spatial_3(x_3)

        x_1 = self.pos_drop(x_1)                    # [64, 11, 11, 32]
        x_2 = self.pos_drop(x_2)
        x_3 = self.pos_drop(x_3)

        if self.model_type in ['Parallel MT', 'Interval MT', 'Series MT', 'Series TM']:
            LG_1 = rearrange(x_1, 'b h w c -> b (h w) c')   # [64, 121, 32]
            LG_2 = rearrange(x_2, 'b h w c -> b (h w) c')
            LG_3 = rearrange(x_3, 'b h w c -> b (h w) c')
            LG = torch.cat([LG_1, LG_2, LG_3], dim=1)

            if self.model_type == 'Parallel MT':
                for i in range(self.depth):
                    LG_1 = self.transformer_1[i](LG[:, 0:LG_1.shape[1], :], mask=None)
                    LG_2 = self.transformer_2[i](LG[:, LG_1.shape[1]:LG_1.shape[1] + LG_2.shape[1], :], mask=None)
                    LG_3 = self.transformer_3[i](LG[:, LG_1.shape[1] + LG_2.shape[1]:, :], mask=None)
                    LG_T = torch.cat([LG_1, LG_2, LG_3], dim=1)
                    LG_T = self.FFN[i](LG_T)
                    LG_M = LG + self.drop_path(self.layers[i](self.norm(LG)))
                    LG = LG_T + LG_M 
            elif self.model_type == 'Interval MT':
                for i in range(self.depth):
                    LG_1 = self.transformer_1[i](LG[:, 0:LG_1.shape[1], :], mask=None)
                    LG_2 = self.transformer_2[i](LG[:, LG_1.shape[1]:LG_1.shape[1] + LG_2.shape[1], :], mask=None)
                    LG_3 = self.transformer_3[i](LG[:, LG_1.shape[1] + LG_2.shape[1]:, :], mask=None)
                    LG_T = torch.cat([LG_1, LG_2, LG_3], dim=1)
                    LG_T = self.FFN[i](LG_T)
                    LG = LG_T + self.drop_path(self.layers[i](self.norm(LG_T)))
            elif self.model_type == 'Series TM':
                for i in range(self.depth):
                    LG_1 = self.transformer_1[i](LG[:, 0:LG_1.shape[1], :], mask=None)
                    LG_2 = self.transformer_2[i](LG[:, LG_1.shape[1]:LG_1.shape[1] + LG_2.shape[1], :], mask=None)
                    LG_3 = self.transformer_3[i](LG[:, LG_1.shape[1] + LG_2.shape[1]:, :], mask=None)
                    LG = torch.cat([LG_1, LG_2, LG_3], dim=1)
                    LG = self.FFN[i](LG)
                for i in range(self.depth):
                    LG = LG + self.drop_path(self.layers[i](self.norm(LG)))
            elif self.model_type == 'Series MT':
                for i in range(self.depth):
                    LG = LG + self.drop_path(self.layers[i](self.norm(LG)))
                for i in range(self.depth):
                    LG_1 = self.transformer_1[i](LG[:, 0:LG_1.shape[1], :], mask=None)
                    LG_2 = self.transformer_2[i](LG[:, LG_1.shape[1]:LG_1.shape[1] + LG_2.shape[1], :], mask=None)
                    LG_3 = self.transformer_3[i](LG[:, LG_1.shape[1] + LG_2.shape[1]:, :], mask=None)
                    LG = torch.cat([LG_1, LG_2, LG_3], dim=1)
                    LG = self.FFN[i](LG)
        
        batch_size = x_rcgc.shape[0]
        # Step 1: 转换 x_rcgc 为 [batch_size, embed_dim, 15, 15]
        x_rcgc_4d = x_rcgc.permute(0, 2, 1).view(batch_size, self.embed_dim, hh, ww)
        # Step 2: 拆分 LG 为 LG_1, LG_2, LG_3
        spatial_sizes = [dim[0] * dim[1] for dim in spatial_dims]  # [h_1*w_1, h_2*w_2, h_3*w_3]
        LG_1 = LG[:, :spatial_sizes[0], :]  # [batch_size, h_1*w_1, embed_dim]
        LG_2 = LG[:, spatial_sizes[0]:spatial_sizes[0] + spatial_sizes[1], :]  # [batch_size, h_2*w_2, embed_dim]
        LG_3 = LG[:, spatial_sizes[0] + spatial_sizes[1]:, :]  # [batch_size, h_3*w_3, embed_dim]
        
        # Step 3: 转换为 4D 张量并上采样
        LG_1_4d = LG_1.permute(0, 2, 1).view(batch_size, self.embed_dim, spatial_dims[0][0], spatial_dims[0][1])
        LG_2_4d = LG_2.permute(0, 2, 1).view(batch_size, self.embed_dim, spatial_dims[1][0], spatial_dims[1][1])
        LG_3_4d = LG_3.permute(0, 2, 1).view(batch_size, self.embed_dim, spatial_dims[2][0], spatial_dims[2][1])
        
        LG_1_4d = F.interpolate(LG_1_4d, size=(15, 15), mode='bilinear', align_corners=False)
        LG_2_4d = F.interpolate(LG_2_4d, size=(15, 15), mode='bilinear', align_corners=False)
        LG_3_4d = F.interpolate(LG_3_4d, size=(15, 15), mode='bilinear', align_corners=False)
        
        
         # Step 4: 融合 LG_1, LG_2, LG_3
        LG_fused  = torch.cat([LG_1_4d, LG_2_4d, LG_3_4d], dim=1)
        
        LG_fused = self.conv_fuse(LG_fused)  # [batch_size, embed_dim, 15, 15]
        
        # Step 5: 使用 TIM 模块融合 x_rcgc 和 LG_fused
        feature, _, _ = self.tim(x_rcgc_4d, LG_fused)  # [batch_size, embed_dim, 15, 15]
        
        # Step 6: 全局平均池化并展平，得到 [batch_size, embed_dim]

        # 已知feature的size是torch.Size([64, 32, 15, 15])我想将它的size变成torch.Size([64, 32, 225])
        feature = rearrange(feature, 'b c h w -> b c (h w)')
        feature = feature.permute(0, 2, 1)
        feature = feature.mean(dim=1)
        # feature = LG.mean(dim=1) + x_rcgc.mean(dim=1)
        
        return feature

    def forward(self, x, inference_params=None):
        feature = self.forward_features(x, inference_params)
        x = self.head(self.head_drop(feature))
        return x, feature       # x是分割后的，feature是未分割的
    
# 测试部分
if __name__ == "__main__":
    # 设置随机种子，确保可重复性
    torch.manual_seed(42)
    
    # 模拟DBMGNet中的输入格式
    batch_size = 64
    depth = 1
    channel = 30  # 与DBMGNet中的emb_dim保持一致
    h = w = 15
    hw = h * w
    
    # 创建输入数据，模拟DBMGNet中的输入格式 [batch_size, emb_dim, hw]
    x = torch.randn(batch_size, depth,channel, h, w).to('cuda')
    
    # 初始化模型参数
    net =VisionMamba(
        model_type=config.model_type,
        embed_dim=config.embed_dim,
        d_state=config.d_state,  # 256,  # State dimension of the model
        ssm_ratio = config.ssm_ratio,
        num_classes=config.num_classes,  # Number of output classes
        depth=config.depth,
        pos=config.pos,
        cls=config.cls,
        conv3D_channel=config.conv3D_channel,
        conv3D_kernel_1=config.conv3D_kernel_1,
        conv3D_kernel_2=config.conv3D_kernel_2,
        conv3D_kernel_3=config.conv3D_kernel_3,
        dim_patch=config.dim_patch,
        dim_linear_1=config.dim_linear_1,
        dim_linear_2=config.dim_linear_2,
        dim_linear_3=config.dim_linear_3,
    ).cuda()
    # 打印输入张量形状
    print(f"输入张量形状: {x.shape}")
    
    # 前向传播
    with torch.no_grad():
        output,_ = net(x)
    
    # 打印输出张量形状
    print(f"输出张量形状: {output.shape}")

