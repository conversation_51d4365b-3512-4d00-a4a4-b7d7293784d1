class DefaultConfigs(object):
    # ==================== 多数据集配置 ====================
    # 要运行的数据集列表，可以选择运行单个或多个数据集
    datasets_to_run = [
        'WHU_Hi_LongKou',
        'PaviaU', 
        'Indian',
        'Houston2013',
        # 'QUH-Tangdaowan',
        # 'WHU-Hi-HongHu'
    ]
    
    # 每个数据集的具体配置
    dataset_configs = {
        'PaviaU': {
            'num_classes': 9,
            'flag_list': [1],  # 0按照数量划分, 1按照比例划分
            'train_ratio': 0.05,  # 比例划分时的训练集比例
            'train_num': 200,     # 数量划分时每类训练样本数
            'train_epoch': 100,
            'test_epoch': 5,
        },
        'Indian': {
            # 不足50个按照15个训练
            'num_classes': 16,
            'flag_list': [1],
            'train_ratio': 0.1,
            'train_num': 50,
            'train_epoch': 100,
            'test_epoch': 5,
        },
        'Houston2013': {
            'num_classes': 15,
            'flag_list': [0],
            'train_ratio': 0.1,
            'train_num': 190,
            'train_epoch': 100,
            'test_epoch': 5,
        },
        'WHU_Hi_LongKou': {
            'num_classes': 9,
            'flag_list': [1],
            'train_ratio': 0.01,
            'train_num': 60,
            'train_epoch': 100,
            'test_epoch': 5,
        },
        'QUH-Tangdaowan': {
            'num_classes': 18,
            'flag_list': [1],
            'train_ratio': 0.1,
            'train_num': 50,
            'train_epoch': 50,  # 特殊设置
            'test_epoch': 5,
        },
        'WHU-Hi-HongHu': {
            'num_classes': 22,
            'flag_list': [0],
            'train_ratio': 0.1,
            'train_num': 50,
            'train_epoch': 100,
            'test_epoch': 5,
        }
    }
    
    # ==================== 通用训练参数 ====================
    # SGD优化器参数
    weight_decay = 5e-4
    momentum = 0.9
    # 学习率
    init_lr = 0.001
    # 训练参数
    BATCH_SIZE_TRAIN = 256
    norm_flag = True
    gpus = '0'
    
    # ==================== 模型参数 ====================
    patch_size = 15
    pca_components = 30
    # 模型的种类
    model_type = 'Parallel MT'   # 'Parallel MT'  'Interval MT'  'Series MT'  'Series TM'
    depth = 3      # 深度"3"
    embed_dim = 32      # 嵌入维度
    d_state = 16        # 状态维度
    ssm_ratio = 1       # SSM比例
    pos = False         # 是否使用位置编码
    cls = False         # 是否使用分类头
    
    # 3DConv参数
    conv3D_channel = 32
    conv3D_kernel_1 = (5, 5, 5)
    conv3D_kernel_2 = (7, 7, 7)
    conv3D_kernel_3 = (9, 9, 9)
    dim_patch = patch_size - conv3D_kernel_1[1] + 1
    dim_linear_1 = pca_components - conv3D_kernel_1[0] + 1
    dim_linear_2 = pca_components - conv3D_kernel_2[0] + 1
    dim_linear_3 = pca_components - conv3D_kernel_3[0] + 1
    
    # ==================== 动态配置方法 ====================
    def set_current_dataset(self, dataset_name):
        """设置当前数据集并更新相关配置"""
        if dataset_name not in self.dataset_configs:
            raise ValueError(f"Dataset {dataset_name} not found in dataset_configs")
        
        # 设置当前数据集
        self.data = dataset_name
        
        # 更新数据集特定配置
        dataset_config = self.dataset_configs[dataset_name]
        self.num_classes = dataset_config['num_classes']
        self.flag_list = dataset_config['flag_list']
        self.train_ratio = dataset_config['train_ratio']
        self.test_ratio = 1 - self.train_ratio
        self.train_num = dataset_config['train_num']
        self.train_epoch = dataset_config['train_epoch']
        self.test_epoch = dataset_config['test_epoch']
        
        # 更新路径信息
        self.checkpoint_path = ('./' + "论文原参数对比实验/HSI-Former/" + self.data + '/' + self.model_type + 
                               '_TrainEpoch' + str(self.train_epoch) + '_TestEpoch' + str(self.test_epoch) + 
                               '_Batch' + str(self.BATCH_SIZE_TRAIN) + '/PatchSize' + str(self.patch_size) + 
                               '_TestRatio' + str(self.test_ratio) + '/' + 'Depth' + str(self.depth) + 
                               '_embed' + str(self.embed_dim) + '_dstate' + str(self.d_state) + 
                               '_ratio' + str(self.ssm_ratio) + '_3Dconv' + str(self.conv3D_channel) + 
                               '&' + str(self.conv3D_kernel_1) + '&' + str(self.conv3D_kernel_2) + 
                               '&' + str(self.conv3D_kernel_3) + '/')
        self.logs = self.checkpoint_path
    
    def get_datasets_to_run(self):
        """获取要运行的数据集列表"""
        return self.datasets_to_run
    
    def update_dataset_config(self, dataset_name, **kwargs):
        """更新特定数据集的配置"""
        if dataset_name not in self.dataset_configs:
            raise ValueError(f"Dataset {dataset_name} not found in dataset_configs")
        
        for key, value in kwargs.items():
            if key in self.dataset_configs[dataset_name]:
                self.dataset_configs[dataset_name][key] = value
            else:
                print(f"Warning: {key} is not a valid config key for dataset {dataset_name}")

# 创建配置实例
config = DefaultConfigs()

# 初始化为第一个数据集（如果有的话）
if config.datasets_to_run:
    config.set_current_dataset(config.datasets_to_run[0])
