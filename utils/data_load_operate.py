import scipy.io as sio
from utils.config_multi import config
import os
import torch
import numpy as np
from sklearn.decomposition import PCA
from sklearn.model_selection import train_test_split

# 加载数据集
def loadData():
    if config.data == 'PaviaU':
        data = sio.loadmat('./data/PaviaU/PaviaU.mat')['paviaU']
        labels = sio.loadmat('./data/PaviaU/PaviaU_gt.mat')['paviaU_gt']

    elif config.data == 'Indian':
        data = sio.loadmat('./data/Indian/Indian_pines_corrected.mat')['indian_pines_corrected']
        labels = sio.loadmat('./data/Indian/Indian_pines_gt.mat')['indian_pines_gt']

    elif config.data == 'Houston2018':
        data = sio.loadmat('./data/Houston2018/houstonU2018.mat')['houstonU']
        labels = sio.loadmat('./data/Houston2018/houstonU2018.mat')['houstonU_gt']

    elif config.data == 'Houston2013':  
        data = sio.loadmat('./data/Houston2013/Houston.mat')['Houston']
        labels = sio.loadmat('./data/Houston2013/Houston_gt.mat')['Houston_gt']
        
    elif config.data == 'WHU_Hi_LongKou':       # class 9
        data = sio.loadmat('./data/LongKou/WHU_Hi_LongKou.mat')['WHU_Hi_LongKou']
        labels = sio.loadmat('./data/LongKou/WHU_Hi_LongKou_gt.mat')['WHU_Hi_LongKou_gt']
        
    elif config.data == 'QUH-Tangdaowan':       # class 18
        data = sio.loadmat(os.path.join('./data/Tangdaowan/QUH-Tangdaowan.mat'))['Tangdaowan']
        labels = sio.loadmat(os.path.join('./data/Tangdaowan/QUH-Tangdaowan_GT.mat'))['TangdaowanGT']
        
    elif config.data == 'WHU-Hi-HongHu':       # class 22
        data = sio.loadmat('/data/share/akaxxx/projects/HSI-MFormer-main/data/WHU-Hi-HongHu/WHU_Hi_HongHu.mat')['WHU_Hi_HongHu']
        labels = sio.loadmat('/data/share/akaxxx/projects/HSI-MFormer-main/data/WHU-Hi-HongHu/WHU_Hi_HongHu_gt.mat')['WHU_Hi_HongHu_gt']
    return data, labels

# 采用主成分回归的方式对数据进行降维
def applyPCA(X, numComponents):
    # Principal component analysis on HSI data
    newX = np.reshape(X, (-1, X.shape[2]))
    pca = PCA(n_components=numComponents, whiten=True)
    newX = pca.fit_transform(newX)
    newX = np.reshape(newX, (X.shape[0], X.shape[1], numComponents))

    return newX


# 对数据进行填充
def padWithZeros(X, margin=2):

    newX = np.zeros((X.shape[0] + 2 * margin, X.shape[1] + 2* margin, X.shape[2]))
    x_offset = margin
    y_offset = margin
    newX[x_offset:X.shape[0] + x_offset, y_offset:X.shape[1] + y_offset, :] = X

    return newX

# 创建图像立方体
def createImageCubes(X, y, windowSize, removeZeroLabels = True):

    # padding
    margin = int((windowSize - 1) / 2)
    zeroPaddedX = padWithZeros(X, margin=margin)
    # split patches
    patchesData = np.zeros((X.shape[0] * X.shape[1], windowSize, windowSize, X.shape[2]))  
    patchesLabels = np.zeros((X.shape[0] * X.shape[1]))
    patchIndex = 0
    for r in range(margin, zeroPaddedX.shape[0] - margin):
        for c in range(margin, zeroPaddedX.shape[1] - margin):
            patch = zeroPaddedX[r - margin:r + margin + 1, c - margin:c + margin + 1]
            patchesData[patchIndex, :, :, :] = patch
            patchesLabels[patchIndex] = y[r-margin, c-margin]
            patchIndex = patchIndex + 1
    if removeZeroLabels:
        patchesData = patchesData[patchesLabels>0,:,:,:]
        patchesLabels = patchesLabels[patchesLabels>0]
        patchesLabels -= 1

    return patchesData, patchesLabels

# 分割训练集和测试集（分层采样）
def splitTrainTestSet(X, y, testRatio, randomState=345):
    if config.flag_list[0] == 1:
        # 按比例划分
        train_ratio = config.train_ratio
        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=1-train_ratio, random_state=randomState, stratify=y)
        X_val, y_val = None, None
    else:
        # 按数量划分
        np.random.seed(randomState)
        classes = np.unique(y)
        train_idx = []
        test_idx = []
        train_num = config.train_num
        for cls in classes:
            cls_idx = np.where(y == cls)[0]
            np.random.shuffle(cls_idx)
            n_total = len(cls_idx)
            if n_total < 80:
                n_train = min(15, n_total)
            else:
                n_train = min(train_num, n_total)
            train_idx.extend(cls_idx[:n_train])
            test_idx.extend(cls_idx[n_train:])
        train_idx = np.array(train_idx)
        test_idx = np.array(test_idx)
        X_train, y_train = X[train_idx], y[train_idx]
        X_test, y_test = X[test_idx], y[test_idx]
        X_val, y_val = None, None
    return X_train, X_val, X_test, y_train, y_val, y_test

""" Training dataset"""
# 将原始数据转化成Pytorch的格式方便后面处理
class TrainDS(torch.utils.data.Dataset):

    def __init__(self, Xtrain, ytrain):

        self.len = Xtrain.shape[0]
        self.x_data = torch.FloatTensor(Xtrain)
        self.y_data = torch.LongTensor(ytrain)

    def __getitem__(self, index):

        return self.x_data[index], self.y_data[index]
    def __len__(self):

        return self.len

""" Testing dataset"""

class TestDS(torch.utils.data.Dataset):

    def __init__(self, Xtest, ytest):

        self.len = Xtest.shape[0]
        self.x_data = torch.FloatTensor(Xtest)
        self.y_data = torch.LongTensor(ytest)

    def __getitem__(self, index):

        return self.x_data[index], self.y_data[index]

    def __len__(self):

        return self.len
