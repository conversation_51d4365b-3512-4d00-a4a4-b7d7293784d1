
==================================================
Dataset: QUH-Tangdaowan
Number of classes: 18
Split method: Ratio
Train ratio/num: 0.1
Train epochs: 50
Test epochs: 5
==================================================
[Epoch: 1]   [loss avg: 0.3091]   [acc: 91.62] 
[Epoch: 2]   [loss avg: 0.0568]   [acc: 98.21] 
[Epoch: 3]   [loss avg: 0.0298]   [acc: 99.05] 
[Epoch: 4]   [loss avg: 0.0185]   [acc: 99.40] 
[Epoch: 5]   [loss avg: 0.0187]   [acc: 99.37] 
[Epoch: 6]   [loss avg: 0.0095]   [acc: 99.72] 
[Epoch: 7]   [loss avg: 0.0105]   [acc: 99.67] 
[Epoch: 8]   [loss avg: 0.0094]   [acc: 99.69] 
[Epoch: 9]   [loss avg: 0.0054]   [acc: 99.85] 
[Epoch: 10]   [loss avg: 0.0058]   [acc: 99.83] 
[Epoch: 11]   [loss avg: 0.0066]   [acc: 99.78] 
[Epoch: 12]   [loss avg: 0.0072]   [acc: 99.78] 
[Epoch: 13]   [loss avg: 0.0051]   [acc: 99.84] 
[Epoch: 14]   [loss avg: 0.0039]   [acc: 99.89] 
[Epoch: 15]   [loss avg: 0.0036]   [acc: 99.91] 
[Epoch: 16]   [loss avg: 0.0055]   [acc: 99.84] 
[Epoch: 17]   [loss avg: 0.0057]   [acc: 99.82] 
[Epoch: 18]   [loss avg: 0.0021]   [acc: 99.94] 
[Epoch: 19]   [loss avg: 0.0019]   [acc: 99.95] 
[Epoch: 20]   [loss avg: 0.0057]   [acc: 99.83] 
[Epoch: 21]   [loss avg: 0.0061]   [acc: 99.81] 
[Epoch: 22]   [loss avg: 0.0018]   [acc: 99.95] 
[Epoch: 23]   [loss avg: 0.0019]   [acc: 99.93] 
[Epoch: 24]   [loss avg: 0.0023]   [acc: 99.94] 
[Epoch: 25]   [loss avg: 0.0020]   [acc: 99.93] 
[Epoch: 26]   [loss avg: 0.0066]   [acc: 99.81] 
[Epoch: 27]   [loss avg: 0.0075]   [acc: 99.82] 
[Epoch: 28]   [loss avg: 0.0013]   [acc: 99.96] 
[Epoch: 29]   [loss avg: 0.0008]   [acc: 99.97] 
[Epoch: 30]   [loss avg: 0.0027]   [acc: 99.91] 
[Epoch: 31]   [loss avg: 0.0033]   [acc: 99.90] 
[Epoch: 32]   [loss avg: 0.0021]   [acc: 99.93] 
[Epoch: 33]   [loss avg: 0.0026]   [acc: 99.93] 
[Epoch: 34]   [loss avg: 0.0020]   [acc: 99.94] 
[Epoch: 35]   [loss avg: 0.0021]   [acc: 99.92] 
[Epoch: 36]   [loss avg: 0.0011]   [acc: 99.97] 
[Epoch: 37]   [loss avg: 0.0015]   [acc: 99.96] 
[Epoch: 38]   [loss avg: 0.0011]   [acc: 99.97] 
[Epoch: 39]   [loss avg: 0.0043]   [acc: 99.87] 
[Epoch: 40]   [loss avg: 0.0024]   [acc: 99.93] 
[Epoch: 41]   [loss avg: 0.0014]   [acc: 99.95] 
[Epoch: 42]   [loss avg: 0.0013]   [acc: 99.97] 
[Epoch: 43]   [loss avg: 0.0024]   [acc: 99.93] 
[Epoch: 44]   [loss avg: 0.0023]   [acc: 99.92] 
[Epoch: 45]   [loss avg: 0.0018]   [acc: 99.93] 
[Epoch: 46]   [loss avg: 0.0036]   [acc: 99.91] 
[Epoch: 47]   [loss avg: 0.0044]   [acc: 99.90] 
[Epoch: 48]   [loss avg: 0.0005]   [acc: 99.99] 
[Epoch: 49]   [loss avg: 0.0011]   [acc: 99.98] 
[Epoch: 50]   [loss avg: 0.0005]   [acc: 99.98] 
Finished TrainingTest_Epoch: 1: Each_OA: 99.85, Each_AA: 99.69, Each_kappa: 99.83 
