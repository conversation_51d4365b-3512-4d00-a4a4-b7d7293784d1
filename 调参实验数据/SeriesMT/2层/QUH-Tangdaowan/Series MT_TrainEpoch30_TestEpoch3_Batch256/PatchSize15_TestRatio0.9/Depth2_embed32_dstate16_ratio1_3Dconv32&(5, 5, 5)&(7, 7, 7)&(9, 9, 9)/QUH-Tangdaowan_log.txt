
==================================================
Dataset: QUH-Tangdaowan
Number of classes: 18
Split method: Ratio
Train ratio/num: 0.1
Train epochs: 30
Test epochs: 3
==================================================
[Epoch: 1]   [loss avg: 0.2794]   [acc: 92.90] 
[Epoch: 2]   [loss avg: 0.0434]   [acc: 98.66] 
[Epoch: 3]   [loss avg: 0.0255]   [acc: 99.19] 
[Epoch: 4]   [loss avg: 0.0180]   [acc: 99.41] 
[Epoch: 5]   [loss avg: 0.0135]   [acc: 99.57] 
[Epoch: 6]   [loss avg: 0.0098]   [acc: 99.68] 
[Epoch: 7]   [loss avg: 0.0097]   [acc: 99.70] 
[Epoch: 8]   [loss avg: 0.0079]   [acc: 99.75] 
[Epoch: 9]   [loss avg: 0.0095]   [acc: 99.69] 
[Epoch: 10]   [loss avg: 0.0035]   [acc: 99.89] 
[Epoch: 11]   [loss avg: 0.0044]   [acc: 99.87] 
[Epoch: 12]   [loss avg: 0.0069]   [acc: 99.76] 
[Epoch: 13]   [loss avg: 0.0077]   [acc: 99.75] 
[Epoch: 14]   [loss avg: 0.0057]   [acc: 99.83] 
[Epoch: 15]   [loss avg: 0.0025]   [acc: 99.92] 
[Epoch: 16]   [loss avg: 0.0067]   [acc: 99.77] 
[Epoch: 17]   [loss avg: 0.0031]   [acc: 99.90] 
[Epoch: 18]   [loss avg: 0.0013]   [acc: 99.96] 
[Epoch: 19]   [loss avg: 0.0034]   [acc: 99.88] 
[Epoch: 20]   [loss avg: 0.0068]   [acc: 99.79] 
[Epoch: 21]   [loss avg: 0.0054]   [acc: 99.83] 
[Epoch: 22]   [loss avg: 0.0024]   [acc: 99.92] 
[Epoch: 23]   [loss avg: 0.0016]   [acc: 99.96] 
[Epoch: 24]   [loss avg: 0.0018]   [acc: 99.95] 
[Epoch: 25]   [loss avg: 0.0008]   [acc: 99.98] 
[Epoch: 26]   [loss avg: 0.0075]   [acc: 99.76] 
[Epoch: 27]   [loss avg: 0.0047]   [acc: 99.84] 
[Epoch: 28]   [loss avg: 0.0022]   [acc: 99.93] 
[Epoch: 29]   [loss avg: 0.0014]   [acc: 99.95] 
[Epoch: 30]   [loss avg: 0.0006]   [acc: 99.99] 
Finished TrainingTest_Epoch: 1: Each_OA: 99.85, Each_AA: 99.74, Each_kappa: 99.83 
