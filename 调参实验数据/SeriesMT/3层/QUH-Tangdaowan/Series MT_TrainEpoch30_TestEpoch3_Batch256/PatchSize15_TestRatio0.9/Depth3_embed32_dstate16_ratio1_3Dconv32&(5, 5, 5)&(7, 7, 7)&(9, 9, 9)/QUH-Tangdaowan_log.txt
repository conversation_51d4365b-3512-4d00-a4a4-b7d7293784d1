
==================================================
Dataset: QUH-Tangdaowan
Number of classes: 18
Split method: Ratio
Train ratio/num: 0.1
Train epochs: 30
Test epochs: 3
==================================================
[Epoch: 1]   [loss avg: 0.2912]   [acc: 92.26] 
[Epoch: 2]   [loss avg: 0.0459]   [acc: 98.54] 
[Epoch: 3]   [loss avg: 0.0231]   [acc: 99.26] 
[Epoch: 4]   [loss avg: 0.0185]   [acc: 99.42] 
[Epoch: 5]   [loss avg: 0.0135]   [acc: 99.58] 
[Epoch: 6]   [loss avg: 0.0119]   [acc: 99.62] 
[Epoch: 7]   [loss avg: 0.0103]   [acc: 99.67] 
[Epoch: 8]   [loss avg: 0.0084]   [acc: 99.72] 
[Epoch: 9]   [loss avg: 0.0070]   [acc: 99.77] 
[Epoch: 10]   [loss avg: 0.0064]   [acc: 99.80] 
[Epoch: 11]   [loss avg: 0.0068]   [acc: 99.77] 
[Epoch: 12]   [loss avg: 0.0055]   [acc: 99.80] 
[Epoch: 13]   [loss avg: 0.0080]   [acc: 99.78] 
[Epoch: 14]   [loss avg: 0.0031]   [acc: 99.90] 
[Epoch: 15]   [loss avg: 0.0064]   [acc: 99.80] 
[Epoch: 16]   [loss avg: 0.0040]   [acc: 99.88] 
[Epoch: 17]   [loss avg: 0.0067]   [acc: 99.78] 
[Epoch: 18]   [loss avg: 0.0021]   [acc: 99.94] 
[Epoch: 19]   [loss avg: 0.0078]   [acc: 99.74] 
[Epoch: 20]   [loss avg: 0.0031]   [acc: 99.91] 
[Epoch: 21]   [loss avg: 0.0021]   [acc: 99.93] 
[Epoch: 22]   [loss avg: 0.0024]   [acc: 99.92] 
[Epoch: 23]   [loss avg: 0.0064]   [acc: 99.81] 
[Epoch: 24]   [loss avg: 0.0028]   [acc: 99.90] 
[Epoch: 25]   [loss avg: 0.0018]   [acc: 99.94] 
[Epoch: 26]   [loss avg: 0.0056]   [acc: 99.83] 
[Epoch: 27]   [loss avg: 0.0013]   [acc: 99.95] 
[Epoch: 28]   [loss avg: 0.0036]   [acc: 99.88] 
[Epoch: 29]   [loss avg: 0.0040]   [acc: 99.87] 
[Epoch: 30]   [loss avg: 0.0031]   [acc: 99.91] 
Finished TrainingTest_Epoch: 1: Each_OA: 99.81, Each_AA: 99.76, Each_kappa: 99.78 
