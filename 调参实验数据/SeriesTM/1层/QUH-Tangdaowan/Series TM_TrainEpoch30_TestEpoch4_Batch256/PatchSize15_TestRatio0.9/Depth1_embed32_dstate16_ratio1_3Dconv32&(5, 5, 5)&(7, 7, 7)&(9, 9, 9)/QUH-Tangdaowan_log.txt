
==================================================
Dataset: QUH-Tangdaowan
Number of classes: 18
Split method: Ratio
Train ratio/num: 0.1
Train epochs: 30
Test epochs: 4
==================================================
[Epoch: 1]   [loss avg: 0.3331]   [acc: 91.40] 
[Epoch: 2]   [loss avg: 0.0583]   [acc: 98.14] 
[Epoch: 3]   [loss avg: 0.0304]   [acc: 99.05] 
[Epoch: 4]   [loss avg: 0.0262]   [acc: 99.13] 
[Epoch: 5]   [loss avg: 0.0144]   [acc: 99.54] 
[Epoch: 6]   [loss avg: 0.0120]   [acc: 99.61] 
[Epoch: 7]   [loss avg: 0.0108]   [acc: 99.63] 
[Epoch: 8]   [loss avg: 0.0084]   [acc: 99.75] 
[Epoch: 9]   [loss avg: 0.0069]   [acc: 99.80] 
[Epoch: 10]   [loss avg: 0.0094]   [acc: 99.73] 
[Epoch: 11]   [loss avg: 0.0067]   [acc: 99.78] 
[Epoch: 12]   [loss avg: 0.0040]   [acc: 99.90] 
[Epoch: 13]   [loss avg: 0.0029]   [acc: 99.92] 
[Epoch: 14]   [loss avg: 0.0044]   [acc: 99.88] 
[Epoch: 15]   [loss avg: 0.0054]   [acc: 99.83] 
[Epoch: 16]   [loss avg: 0.0026]   [acc: 99.92] 
[Epoch: 17]   [loss avg: 0.0024]   [acc: 99.92] 
[Epoch: 18]   [loss avg: 0.0063]   [acc: 99.80] 
[Epoch: 19]   [loss avg: 0.0017]   [acc: 99.96] 
[Epoch: 20]   [loss avg: 0.0015]   [acc: 99.95] 
[Epoch: 21]   [loss avg: 0.0089]   [acc: 99.75] 
[Epoch: 22]   [loss avg: 0.0034]   [acc: 99.90] 
[Epoch: 23]   [loss avg: 0.0024]   [acc: 99.93] 
[Epoch: 24]   [loss avg: 0.0013]   [acc: 99.97] 
[Epoch: 25]   [loss avg: 0.0012]   [acc: 99.96] 
[Epoch: 26]   [loss avg: 0.0019]   [acc: 99.94] 
[Epoch: 27]   [loss avg: 0.0008]   [acc: 99.98] 
[Epoch: 28]   [loss avg: 0.0019]   [acc: 99.95] 
[Epoch: 29]   [loss avg: 0.0074]   [acc: 99.76] 
[Epoch: 30]   [loss avg: 0.0012]   [acc: 99.97] 
Finished TrainingTest_Epoch: 1: Each_OA: 99.86, Each_AA: 99.79, Each_kappa: 99.84 
