
==================================================
Dataset: QUH-Tangdaowan
Number of classes: 18
Split method: Ratio
Train ratio/num: 0.1
Train epochs: 50
Test epochs: 5
==================================================
[Epoch: 1]   [loss avg: 0.3057]   [acc: 91.68] 
[Epoch: 2]   [loss avg: 0.0493]   [acc: 98.40] 
[Epoch: 3]   [loss avg: 0.0291]   [acc: 99.05] 
[Epoch: 4]   [loss avg: 0.0180]   [acc: 99.44] 
[Epoch: 5]   [loss avg: 0.0150]   [acc: 99.50] 
[Epoch: 6]   [loss avg: 0.0132]   [acc: 99.58] 
[Epoch: 7]   [loss avg: 0.0090]   [acc: 99.71] 
[Epoch: 8]   [loss avg: 0.0042]   [acc: 99.88] 
[Epoch: 9]   [loss avg: 0.0067]   [acc: 99.78] 
[Epoch: 10]   [loss avg: 0.0077]   [acc: 99.75] 
[Epoch: 11]   [loss avg: 0.0063]   [acc: 99.81] 
[Epoch: 12]   [loss avg: 0.0060]   [acc: 99.79] 
[Epoch: 13]   [loss avg: 0.0066]   [acc: 99.80] 
[Epoch: 14]   [loss avg: 0.0032]   [acc: 99.90] 
[Epoch: 15]   [loss avg: 0.0014]   [acc: 99.96] 
[Epoch: 16]   [loss avg: 0.0038]   [acc: 99.87] 
[Epoch: 17]   [loss avg: 0.0051]   [acc: 99.85] 
[Epoch: 18]   [loss avg: 0.0031]   [acc: 99.90] 
[Epoch: 19]   [loss avg: 0.0037]   [acc: 99.88] 
[Epoch: 20]   [loss avg: 0.0048]   [acc: 99.84] 
[Epoch: 21]   [loss avg: 0.0008]   [acc: 99.98] 
[Epoch: 22]   [loss avg: 0.0015]   [acc: 99.96] 
[Epoch: 23]   [loss avg: 0.0007]   [acc: 99.98] 
[Epoch: 24]   [loss avg: 0.0004]   [acc: 99.99] 
[Epoch: 25]   [loss avg: 0.0019]   [acc: 99.95] 
[Epoch: 26]   [loss avg: 0.0091]   [acc: 99.73] 
[Epoch: 27]   [loss avg: 0.0031]   [acc: 99.90] 
[Epoch: 28]   [loss avg: 0.0017]   [acc: 99.95] 
[Epoch: 29]   [loss avg: 0.0009]   [acc: 99.98] 
[Epoch: 30]   [loss avg: 0.0038]   [acc: 99.90] 
[Epoch: 31]   [loss avg: 0.0006]   [acc: 99.99] 
[Epoch: 32]   [loss avg: 0.0002]   [acc: 99.99] 
[Epoch: 33]   [loss avg: 0.0005]   [acc: 99.98] 
[Epoch: 34]   [loss avg: 0.0030]   [acc: 99.92] 
[Epoch: 35]   [loss avg: 0.0037]   [acc: 99.89] 
[Epoch: 36]   [loss avg: 0.0013]   [acc: 99.97] 
[Epoch: 37]   [loss avg: 0.0008]   [acc: 99.97] 
[Epoch: 38]   [loss avg: 0.0036]   [acc: 99.89] 
[Epoch: 39]   [loss avg: 0.0033]   [acc: 99.90] 
[Epoch: 40]   [loss avg: 0.0004]   [acc: 99.99] 
[Epoch: 41]   [loss avg: 0.0004]   [acc: 99.99] 
[Epoch: 42]   [loss avg: 0.0004]   [acc: 99.99] 
[Epoch: 43]   [loss avg: 0.0001]   [acc: 100.00] 
[Epoch: 44]   [loss avg: 0.0002]   [acc: 99.99] 
[Epoch: 45]   [loss avg: 0.0081]   [acc: 99.75] 
[Epoch: 46]   [loss avg: 0.0006]   [acc: 99.99] 
[Epoch: 47]   [loss avg: 0.0001]   [acc: 100.00] 
[Epoch: 48]   [loss avg: 0.0012]   [acc: 99.96] 
[Epoch: 49]   [loss avg: 0.0019]   [acc: 99.95] 
[Epoch: 50]   [loss avg: 0.0001]   [acc: 100.00] 
Finished TrainingTest_Epoch: 1: Each_OA: 99.90, Each_AA: 99.82, Each_kappa: 99.88 
