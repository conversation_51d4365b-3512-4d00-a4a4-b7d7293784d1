
==================================================
Dataset: QUH-Tangdaowan
Number of classes: 18
Split method: Ratio
Train ratio/num: 0.1
Train epochs: 30
Test epochs: 4
==================================================
[Epoch: 1]   [loss avg: 0.2759]   [acc: 92.89] 
[Epoch: 2]   [loss avg: 0.0491]   [acc: 98.42] 
[Epoch: 3]   [loss avg: 0.0288]   [acc: 99.07] 
[Epoch: 4]   [loss avg: 0.0196]   [acc: 99.38] 
[Epoch: 5]   [loss avg: 0.0157]   [acc: 99.46] 
[Epoch: 6]   [loss avg: 0.0103]   [acc: 99.69] 
[Epoch: 7]   [loss avg: 0.0088]   [acc: 99.74] 
[Epoch: 8]   [loss avg: 0.0080]   [acc: 99.73] 
[Epoch: 9]   [loss avg: 0.0101]   [acc: 99.69] 
[Epoch: 10]   [loss avg: 0.0061]   [acc: 99.81] 
[Epoch: 11]   [loss avg: 0.0056]   [acc: 99.81] 
[Epoch: 12]   [loss avg: 0.0058]   [acc: 99.82] 
[Epoch: 13]   [loss avg: 0.0034]   [acc: 99.90] 
[Epoch: 14]   [loss avg: 0.0033]   [acc: 99.90] 
[Epoch: 15]   [loss avg: 0.0073]   [acc: 99.76] 
[Epoch: 16]   [loss avg: 0.0049]   [acc: 99.83] 
[Epoch: 17]   [loss avg: 0.0026]   [acc: 99.93] 
[Epoch: 18]   [loss avg: 0.0019]   [acc: 99.93] 
[Epoch: 19]   [loss avg: 0.0014]   [acc: 99.96] 
[Epoch: 20]   [loss avg: 0.0030]   [acc: 99.91] 
[Epoch: 21]   [loss avg: 0.0077]   [acc: 99.75] 
[Epoch: 22]   [loss avg: 0.0015]   [acc: 99.95] 
[Epoch: 23]   [loss avg: 0.0008]   [acc: 99.98] 
[Epoch: 24]   [loss avg: 0.0007]   [acc: 99.98] 
[Epoch: 25]   [loss avg: 0.0046]   [acc: 99.87] 
[Epoch: 26]   [loss avg: 0.0061]   [acc: 99.84] 
[Epoch: 27]   [loss avg: 0.0006]   [acc: 99.98] 
[Epoch: 28]   [loss avg: 0.0015]   [acc: 99.96] 
[Epoch: 29]   [loss avg: 0.0018]   [acc: 99.96] 
[Epoch: 30]   [loss avg: 0.0012]   [acc: 99.97] 
Finished TrainingTest_Epoch: 1: Each_OA: 99.89, Each_AA: 99.78, Each_kappa: 99.87 
